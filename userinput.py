#!/usr/bin/env python3
"""
Simple User Input Script for Layout Bricks Instructions Analyzer

This script provides a simple way to get user input and run analysis.

Usage:
    python userinput.py
"""

def main():
    """Simple user input function."""
    user_input = input("Enter your input (or 'stop' to exit): ").strip()

    if user_input.lower() == "stop":
        print("Stopping...")
        return False

    print(f"You entered: {user_input}")
    return True


if __name__ == "__main__":
    print("Simple user input script started.")

    while True:
        if not main():
            break

    print("<PERSON><PERSON><PERSON> ended.")
