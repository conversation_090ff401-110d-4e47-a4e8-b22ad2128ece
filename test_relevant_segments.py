#!/usr/bin/env python3
"""
Test script to verify that the relevant_segments field is properly handled
in the Layout Bricks Instructions Analyzer.
"""

import json
import tempfile
import os
from pathlib import Path

# Import our modules
from analyzer import LayoutBricksAnalyzer
from csv_handler import save_to_csv
from file_utils import save_reasoning_file

def test_relevant_segments_handling():
    """Test that relevant_segments field is properly handled in all components."""
    
    print("Testing relevant_segments field handling...")
    
    # Create test data with relevant_segments field
    test_result = {
        "file_path": "test_file.txt",
        "score": 8,
        "explanation": "Test explanation with detailed instructions",
        "relevant_segments": "[00:01:30,500 -> 00:02:45,200]; [00:05:10,100 -> 00:06:30,800]",
        "error": None
    }
    
    # Test CSV handler
    print("1. Testing CSV handler...")
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        save_to_csv([test_result], temp_path)
        
        csv_file = temp_path / "analysis_results.csv"
        if csv_file.exists():
            with open(csv_file, 'r', encoding='utf-8') as f:
                csv_content = f.read()
                print(f"   CSV content preview: {csv_content[:200]}...")
                if "relevant_segments" in csv_content:
                    print("   ✓ CSV handler correctly includes relevant_segments field")
                else:
                    print("   ✗ CSV handler missing relevant_segments field")
        else:
            print("   ✗ CSV file was not created")
    
    # Test reasoning file handler
    print("2. Testing reasoning file handler...")
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        save_reasoning_file(test_result, temp_path)
        
        reasoning_files = list(temp_path.glob("*_analysis.txt"))
        if reasoning_files:
            with open(reasoning_files[0], 'r', encoding='utf-8') as f:
                reasoning_content = f.read()
                print(f"   Reasoning file preview: {reasoning_content[:300]}...")
                if "Relevant Segments:" in reasoning_content:
                    print("   ✓ Reasoning file correctly includes relevant_segments field")
                else:
                    print("   ✗ Reasoning file missing relevant_segments field")
        else:
            print("   ✗ Reasoning file was not created")
    
    # Test with empty relevant_segments
    print("3. Testing with empty relevant_segments...")
    test_result_empty = test_result.copy()
    test_result_empty["relevant_segments"] = ""
    test_result_empty["score"] = 2
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        save_to_csv([test_result_empty], temp_path)
        save_reasoning_file(test_result_empty, temp_path)
        
        csv_file = temp_path / "analysis_results.csv"
        if csv_file.exists():
            with open(csv_file, 'r', encoding='utf-8') as f:
                csv_content = f.read()
                if '""' in csv_content or ',,' in csv_content:
                    print("   ✓ Empty relevant_segments handled correctly in CSV")
                else:
                    print("   ✗ Empty relevant_segments not handled correctly in CSV")
    
    # Test with list format (for backward compatibility)
    print("4. Testing with list format relevant_segments...")
    test_result_list = test_result.copy()
    test_result_list["relevant_segments"] = ["[00:01:30,500 -> 00:02:45,200]", "[00:05:10,100 -> 00:06:30,800]"]
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        save_to_csv([test_result_list], temp_path)
        save_reasoning_file(test_result_list, temp_path)
        
        csv_file = temp_path / "analysis_results.csv"
        if csv_file.exists():
            with open(csv_file, 'r', encoding='utf-8') as f:
                csv_content = f.read()
                if ";" in csv_content:
                    print("   ✓ List format relevant_segments converted correctly in CSV")
                else:
                    print("   ✗ List format relevant_segments not converted correctly in CSV")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_relevant_segments_handling()
