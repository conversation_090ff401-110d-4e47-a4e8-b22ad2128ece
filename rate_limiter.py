"""
Rate limiting functionality for API calls.

This module provides rate limiting to ensure API rate limits are not exceeded
when making concurrent requests.
"""

import asyncio
import time
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiter to ensure we don't exceed API rate limits."""

    def __init__(self, max_calls_per_minute: int = 18):
        self.max_calls_per_minute = max_calls_per_minute
        self.calls = []
        self.lock = asyncio.Lock()

    async def acquire(self):
        """Acquire permission to make an API call."""
        async with self.lock:
            now = time.time()
            # Remove calls older than 1 minute
            self.calls = [call_time for call_time in self.calls if now - call_time < 60]

            # If we're at the limit, wait until we can make another call
            if len(self.calls) >= self.max_calls_per_minute:
                # Wait until the oldest call is more than 1 minute old
                oldest_call = min(self.calls)
                wait_time = 60 - (now - oldest_call) + 0.1  # Add small buffer
                if wait_time > 0:
                    logger.info(f"Rate limit reached. Waiting {wait_time:.1f} seconds...")
                    await asyncio.sleep(wait_time)
                    # Refresh the calls list after waiting
                    now = time.time()
                    self.calls = [call_time for call_time in self.calls if now - call_time < 60]

            # Record this call
            self.calls.append(now)
