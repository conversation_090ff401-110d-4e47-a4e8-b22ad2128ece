"""
Configuration management for the Layout Bricks Instructions Analyzer.

This module handles environment variables, logging setup, and application
configuration.
"""

import os
import logging

# Try to load .env file if python-dotenv is available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv not installed, continue without it


def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('analysis.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def get_api_key() -> str:
    """Get the OpenRouter API key from environment variables."""
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        raise ValueError("OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable.")
    return api_key


class Config:
    """Application configuration class."""

    def __init__(self):
        self.model = "openrouter/deepseek/deepseek-r1-0528:free"
        self.default_prompt_file = "_prompt.txt"
        self.default_input_folder = "input_ride"
        self.default_output_folder = "output_ride"
        self.default_max_workers = 4
        self.default_max_calls_per_minute = 18
        self.temperature = 0.1
        self.max_tokens = 1000

    @property
    def api_key(self) -> str:
        """Get API key from environment."""
        return get_api_key()
