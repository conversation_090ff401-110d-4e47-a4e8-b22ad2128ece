#!/usr/bin/env python3
"""
Layout Bricks Instructions Analyzer - Main Entry Point

This script analyzes text files specified in a file list to detect
"layout bricks instructions" using LiteLLM with OpenRouter's DeepSeek model.
Now supports parallel processing with rate limiting and file list input.

Requirements:
- litellm
- openrouter API key
- asyncio (built-in)
- requests (for API call checking)

Usage:
    python main.py --file-list <file_list.txt> [--base-folder <base_folder>] [--output-folder <output_folder>]
"""

import argparse
import requests
from pathlib import Path

from config import setup_logging, get_api_key
from analyzer import LayoutBricksAnalyzer

# Default configuration constants
DEFAULT_BASE_FOLDER = r"D:\Temp\d\a_txt_m4a_ts"
DEFAULT_OUTPUT_FOLDER = r"D:\Temp\d\a_txt_or_analysis"
DEFAULT_FILE_LIST = r"D:\Temp\d\oral.csv"
DEFAULT_PROMPT_FILE = "_prompt.txt"
DEFAULT_MAX_WORKERS = 4
DEFAULT_MAX_CALLS_PER_MINUTE = 18

logger = setup_logging()


def read_file_list(file_list_path: str, base_folder: str) -> list:
    """Read file paths from a file list and combine with base folder."""
    file_paths = []
    base_path = Path(base_folder)

    try:
        with open(file_list_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    # Combine base folder with relative path
                    full_path = base_path / line
                    file_paths.append(full_path)

        logger.info(f"Read {len(file_paths)} file paths from {file_list_path}")
        return file_paths

    except FileNotFoundError:
        logger.error(f"File list not found: {file_list_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading file list {file_list_path}: {e}")
        raise


def check_api_calls_remaining(api_key: str = None) -> dict:
    """Check remaining free API calls on OpenRouter."""
    try:
        if not api_key:
            api_key = get_api_key()

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        response = requests.get('https://openrouter.ai/api/v1/auth/key', headers=headers)
        response.raise_for_status()

        data = response.json()
        return {
            'success': True,
            'data': data,
            'remaining_calls': data.get('usage', {}).get('remaining', 'Unknown')
        }
    except Exception as e:
        logger.error(f"Failed to check API calls: {e}")
        return {
            'success': False,
            'error': str(e),
            'remaining_calls': 'Unknown'
        }


def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description="Analyze text files for layout bricks instructions")
    parser.add_argument("--file-list", default=DEFAULT_FILE_LIST, help=f"Path to file containing list of relative file paths to analyze (default: {DEFAULT_FILE_LIST})")
    parser.add_argument("--base-folder", default=DEFAULT_BASE_FOLDER, help=f"Base folder path to combine with relative file paths (default: {DEFAULT_BASE_FOLDER})")
    parser.add_argument("--output-folder", default=DEFAULT_OUTPUT_FOLDER, help=f"Path to the output folder (default: {DEFAULT_OUTPUT_FOLDER})")
    parser.add_argument("--api-key", help="OpenRouter API key (optional, can use OPENROUTER_API_KEY env var)")
    parser.add_argument("--prompt-file", default=DEFAULT_PROMPT_FILE, help=f"Path to the prompt template file (default: {DEFAULT_PROMPT_FILE})")
    parser.add_argument("--max-workers", type=int, default=DEFAULT_MAX_WORKERS, help=f"Maximum number of parallel workers (default: {DEFAULT_MAX_WORKERS})")
    parser.add_argument("--max-calls-per-minute", type=int, default=DEFAULT_MAX_CALLS_PER_MINUTE, help=f"Maximum API calls per minute (default: {DEFAULT_MAX_CALLS_PER_MINUTE})")

    args = parser.parse_args()

    # Check API calls at startup
    print("Checking remaining API calls...")
    api_key = args.api_key
    try:
        if not api_key:
            api_key = get_api_key()
        result = check_api_calls_remaining(api_key)
        if result['success']:
            print(f"Remaining API calls: {result['remaining_calls']}")
        else:
            print(f"Failed to check API calls: {result['error']}")
    except Exception as e:
        print(f"Warning: Could not check API calls: {e}")



    try:
        # Read file list and combine with base folder
        file_paths = read_file_list(args.file_list, args.base_folder)

        if not file_paths:
            print("No files found in the file list.")
            return 1

        print(f"Found {len(file_paths)} files to analyze from file list.")

        # Initialize analyzer
        analyzer = LayoutBricksAnalyzer(
            api_key=api_key,
            prompt_file=args.prompt_file,
            max_workers=args.max_workers,
            max_calls_per_minute=args.max_calls_per_minute
        )

        # Run analysis on file list
        results = analyzer.analyze_file_list(file_paths, args.output_folder)

        # Print summary
        print(f"\n{'='*60}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*60}")

        if not results:
            print("No new files were processed in this run.")
            print("All files may have been processed already.")
        else:
            for result in results:
                print(f"File: {result['file_path']}")
                print(f"Score: {result['score']}/10")
                if 'confidence' in result:
                    print(f"Confidence: {result['confidence']}")
                if result.get('error'):
                    print(f"Error: {result['error']}")
                print("-" * 40)

            # Statistics
            scores = [r['score'] for r in results if not r.get('error')]
            if scores:
                avg_score = sum(scores) / len(scores)
                high_scores = len([s for s in scores if s >= 7])
                print(f"\nStatistics for this run:")
                print(f"New files analyzed: {len(results)}")
                print(f"Average score: {avg_score:.2f}")
                print(f"Files with high scores (7+): {high_scores}")

        print(f"\nResults saved to CSV and JSON in the output folder.")
        print(f"CSV file: analysis_results.csv")
        print(f"JSON file: analysis_summary.json")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
