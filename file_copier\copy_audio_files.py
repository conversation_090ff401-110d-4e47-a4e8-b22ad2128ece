#!/usr/bin/env python3
"""
Script to copy audio files based on files_to_copy.txt
Reads the file list with scores, finds corresponding .mp3 or .m4a files,
and copies them with score prefix to the output folder.
"""

import os
import shutil
from pathlib import Path

def read_files_to_copy(file_path):
    """
    Read the files_to_copy.txt file and extract filename and score pairs.

    Args:
        file_path (str): Path to the files_to_copy.txt file

    Returns:
        list: List of tuples (filename_without_extension, score)
    """
    files_data = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # Skip empty lines
                    continue

                # Split by tab
                parts = line.split('\t')
                if len(parts) != 2:
                    print(f"Warning: Line {line_num} doesn't have expected format (filepath<tab>score): {line}")
                    continue

                filepath, score = parts

                # Extract filename without folder prefix and extension
                # Remove "input_files\" prefix if present
                if filepath.startswith('input_files\\'):
                    filename = filepath[len('input_files\\'):]
                else:
                    filename = os.path.basename(filepath)

                # Remove .txt extension
                if filename.endswith('.txt'):
                    filename = filename[:-4]

                files_data.append((filename, score.strip()))

    except FileNotFoundError:
        print(f"Error: Could not find file {file_path}")
        return []
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return []

    return files_data

def find_audio_file(filename, input_folder):
    """
    Find audio file with given filename in input folder and subfolders recursively.
    Looks for .mp3 and .m4a extensions.

    Args:
        filename (str): Filename without extension
        input_folder (str): Path to input folder

    Returns:
        str or None: Full path to found file, or None if not found
    """
    input_path = Path(input_folder)

    # Try both extensions
    for ext in ['.mp3', '.m4a']:
        target_filename = filename + ext

        # Search recursively using glob pattern
        for file_path in input_path.rglob(target_filename):
            if file_path.is_file():
                return str(file_path)

    return None

def copy_file_with_prefix(source_path, output_folder, score, original_filename):
    """
    Copy file to output folder with score prefix.

    Args:
        source_path (str): Path to source file
        output_folder (str): Path to output folder
        score (str): Score to use as prefix
        original_filename (str): Original filename without extension
    """
    source_file = Path(source_path)
    output_path = Path(output_folder)

    # Create output folder if it doesn't exist
    output_path.mkdir(parents=True, exist_ok=True)

    # Create new filename with score prefix
    extension = source_file.suffix
    new_filename = f"{score} - {original_filename}{extension}"
    destination = output_path / new_filename

    try:
        shutil.copy2(source_path, destination)
        print(f"Copied: {source_file.name} -> {new_filename}")
        return True
    except Exception as e:
        print(f"Error copying {source_path} to {destination}: {e}")
        return False

def main():
    # Configuration
    files_to_copy_path = r"D:\Temp\d\oral.csv"
    folder_with_audio_files = r"D:\Temp\d\a"
    output_folder = r"D:\Temp\d\a_bj    "  # Same as input folder as specified

    print("Starting audio file copy process...")
    print(f"Reading file list from: {files_to_copy_path}")
    print(f"Input folder: {folder_with_audio_files}")
    print(f"Output folder: {output_folder}")
    print("-" * 50)

    # Read the files to copy
    files_data = read_files_to_copy(files_to_copy_path)

    if not files_data:
        print("No files to process. Exiting.")
        return

    print(f"Found {len(files_data)} files to process")
    print("-" * 50)

    # Process each file
    found_count = 0
    copied_count = 0

    for filename, score in files_data:
        print(f"Processing: {filename} (score: {score})")

        # Find the audio file
        audio_file_path = find_audio_file(filename, folder_with_audio_files)

        if audio_file_path:
            print(f"  Found: {os.path.basename(audio_file_path)}")
            found_count += 1

            # Copy the file with score prefix
            if copy_file_with_prefix(audio_file_path, output_folder, score, filename):
                copied_count += 1
        else:
            print(f"  Not found: No .mp3 or .m4a file found for '{filename}'")

    print("-" * 50)
    print(f"Summary:")
    print(f"  Files to process: {len(files_data)}")
    print(f"  Files found: {found_count}")
    print(f"  Files copied: {copied_count}")
    print(f"  Files not found: {len(files_data) - found_count}")

if __name__ == "__main__":
    main()
