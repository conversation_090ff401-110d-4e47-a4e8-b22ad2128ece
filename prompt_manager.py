"""
Prompt management functionality for the Layout Bricks Instructions Analyzer.

This module handles loading and formatting of prompt templates used for
AI analysis of text files.
"""

import logging

logger = logging.getLogger(__name__)


class PromptManager:
    """Manages prompt templates and formatting."""

    def __init__(self, prompt_file: str = "_prompt.txt"):
        """Initialize the prompt manager with a prompt file."""
        self.prompt_file = prompt_file
        self.prompt_template = self.load_prompt_template()

    def load_prompt_template(self) -> str:
        """Load the prompt template from file."""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                template = f.read()
            logger.info(f"Loaded prompt template from: {self.prompt_file}")
            return template
        except FileNotFoundError:
            logger.error(f"Prompt file not found: {self.prompt_file}")
            raise FileNotFoundError(f"Prompt template file '{self.prompt_file}' not found. Please create this file with your analysis prompt.")
        except Exception as e:
            logger.error(f"Error loading prompt template: {e}")
            raise

    def create_analysis_prompt(self, file_content: str) -> str:
        """Create the prompt for analyzing file content using the loaded template."""
        # Use safer replacement method by default to avoid formatting issues with curly braces
        return self.prompt_template.replace("{file_content}", file_content)

    def reload_template(self):
        """Reload the prompt template from file."""
        self.prompt_template = self.load_prompt_template()
