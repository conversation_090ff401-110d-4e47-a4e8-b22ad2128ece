"""
CSV handling functionality for the Layout Bricks Instructions Analyzer.

This module provides functions for saving analysis results to CSV format
with proper formatting and error handling.
"""

import csv
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List

logger = logging.getLogger(__name__)


def save_to_csv(results: List[Dict], output_path: Path):
    """Save results to CSV file."""
    csv_path = output_path / "analysis_results.csv"

    # Define CSV headers
    headers = ['file_path', 'score', 'relevant_segments', 'reasoning', 'keywords_found', 'confidence', 'error', 'analysis_date']

    # Check if file exists to determine if we need to write headers
    file_exists = csv_path.exists()

    with open(csv_path, 'a', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)

        # Write headers only if file doesn't exist
        if not file_exists:
            writer.writeheader()

        # Write results
        for result in results:
            # Convert relevant_segments to string (handle both list and string formats)
            relevant_segments = result.get('relevant_segments', '')
            if isinstance(relevant_segments, list):
                relevant_segments_str = '; '.join(relevant_segments)
            else:
                relevant_segments_str = str(relevant_segments) if relevant_segments else ''

            # Convert keywords_found list to string for CSV
            keywords_str = '; '.join(result.get('keywords_found', [])) if result.get('keywords_found') else ''

            writer.writerow({
                'file_path': result['file_path'],
                'score': result['score'],
                'relevant_segments': relevant_segments_str,
                'reasoning': result.get('reasoning', result.get('explanation', '')),  # Fallback for old data
                'keywords_found': keywords_str,
                'confidence': result.get('confidence', ''),
                'error': result.get('error', ''),
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })

    logger.info(f"Results saved to CSV: {csv_path}")
